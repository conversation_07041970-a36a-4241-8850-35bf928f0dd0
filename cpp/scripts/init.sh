#!/bin/bash

# C++ 项目开发环境初始化脚本
# 包含编译器、构建工具、文档生成、代码分析等所有必要组件

set -e  # 遇到错误时退出

echo "=== 初始化 C++ 开发环境 ==="

# 更新系统包
echo "更新系统包..."
pacman -Syu --noconfirm

# 基础编译工具
echo "安装基础编译工具..."
pacman -S --noconfirm \
    clang \
    gcc \
    cmake \
    ninja \
    make \
    ccache \
    git

# 文档生成工具
echo "安装文档生成工具..."
pacman -S --noconfirm \
    doxygen \
    graphviz \
    python-pygments \
    python-jinja \
    python-yaml \
    plantuml

# 代码分析和格式化工具
echo "安装代码分析工具..."
pacman -S --noconfirm \
    clang-tools-extra \
    cppcheck \
    valgrind \
    gdb \
    lldb

# Python 工具（用于文档生成和脚本）
echo "安装 Python 工具..."
pacman -S --noconfirm \
    python \
    python-pip \
    python-setuptools \
    python-wheel

# 通过 paru 安装 AUR 包
echo "安装 AUR 包..."
if command -v paru &> /dev/null; then
    paru -S --noconfirm \
        include-what-you-use
else
    echo "警告: paru 未安装，跳过 include-what-you-use"
fi

# 验证安装
echo "=== 验证安装 ==="
echo "Clang 版本:"
clang --version | head -1

echo "CMake 版本:"
cmake --version | head -1

echo "Doxygen 版本:"
doxygen --version

echo "ccache 版本:"
ccache --version | head -1

echo "Python 版本:"
python --version

echo "检查 Python 包:"
python -c "import pygments, jinja2; print('Python 依赖包安装成功')" 2>/dev/null || echo "警告: Python 依赖包可能未正确安装"

# 配置 ccache
echo "配置 ccache..."
ccache --set-config=max_size=5G
ccache --set-config=compression=true
echo "ccache 配置完成，最大缓存大小: 5GB"

# 创建必要的目录
echo "创建项目目录结构..."
mkdir -p build
mkdir -p .cpm_cache

echo "=== 初始化完成 ==="
echo "所有组件已安装完成，可以开始 C++ 开发了！"
echo ""
echo "使用方法:"
echo "1. 配置项目: cmake -S . -B build"
echo "2. 编译项目: cmake --build build"
echo "3. 生成文档: cmake --build build --target GenerateDocs"
echo "4. 运行程序: ./build/bin/zexuan"