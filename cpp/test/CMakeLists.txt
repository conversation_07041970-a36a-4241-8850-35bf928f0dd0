# 测试配置 - 作为主项目的一部分
# 这个文件只在主项目启用 BUILD_TESTS 时被包含

# ---- Dependencies ----

CPMAddPackage("gh:doctest/doctest@2.4.12")

# ---- Create binary ----

file(GLOB test_sources CONFIGURE_DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/source/*.cpp)
add_executable(ZexuanTests ${test_sources})

# 链接测试框架和主项目库
target_link_libraries(ZexuanTests
    doctest::doctest
    core
    protocol
)

# 设置包含目录
target_include_directories(ZexuanTests
    PRIVATE
        ${CMAKE_SOURCE_DIR}/include
)

# ---- Add ZexuanTests ----

enable_testing()

include(${doctest_SOURCE_DIR}/scripts/cmake/doctest.cmake)
doctest_discover_tests(ZexuanTests)
