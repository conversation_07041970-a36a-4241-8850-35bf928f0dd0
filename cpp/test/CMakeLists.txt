cmake_minimum_required(VERSION 3.14...3.22)

project(ZexuanTests LANGUAGES CXX)

# ---- Options ----

option(ENABLE_TEST_COVERAGE "Enable test coverage" OFF)
option(TEST_INSTALLED_VERSION "Test the version found by find_package" OFF)

# --- Import tools ----

include(../cmake/tools.cmake)

# ---- Dependencies ----

include(../cmake/CPM.cmake)

CPMAddPackage("gh:doctest/doctest@2.4.9")
CPMAddPackage("gh:TheLartians/Format.cmake@1.7.3")

if(TEST_INSTALLED_VERSION)
  find_package(zexuan REQUIRED)
else()
  # 直接包含主项目作为依赖
  CPMAddPackage(NAME zexuan SOURCE_DIR ${CMAKE_CURRENT_LIST_DIR}/..)
endif()

# ---- Create binary ----

file(GLOB sources CONFIGURE_DEPENDS ${CMAKE_CURRENT_SOURCE_DIR}/source/*.cpp)
add_executable(${PROJECT_NAME} ${sources})

# 链接测试框架和主项目库
target_link_libraries(${PROJECT_NAME} 
    doctest::doctest 
    core 
    protocol
)

set_target_properties(${PROJECT_NAME} PROPERTIES CXX_STANDARD 23)

# 设置包含目录
target_include_directories(${PROJECT_NAME} 
    PRIVATE 
        ${CMAKE_SOURCE_DIR}/include
)

# enable compiler warnings
if(NOT TEST_INSTALLED_VERSION)
  if(CMAKE_CXX_COMPILER_ID MATCHES "Clang" OR CMAKE_CXX_COMPILER_ID MATCHES "GNU")
    target_compile_options(core PUBLIC -Wall -Wpedantic -Wextra -Werror)
    target_compile_options(protocol PUBLIC -Wall -Wpedantic -Wextra -Werror)
  elseif(MSVC)
    target_compile_options(core PUBLIC /W4 /WX)
    target_compile_options(protocol PUBLIC /W4 /WX)
    target_compile_definitions(${PROJECT_NAME} PUBLIC DOCTEST_CONFIG_USE_STD_HEADERS)
  endif()
endif()

# ---- Add ZexuanTests ----

enable_testing()

# Note: doctest and similar testing frameworks can automatically configure CMake tests. For other
# testing frameworks add the tests target instead: add_test(NAME ${PROJECT_NAME} COMMAND
# ${PROJECT_NAME})

include(${doctest_SOURCE_DIR}/scripts/cmake/doctest.cmake)
doctest_discover_tests(${PROJECT_NAME})

# ---- code coverage ----

if(ENABLE_TEST_COVERAGE)
  target_compile_options(core PUBLIC -O0 -g -fprofile-arcs -ftest-coverage)
  target_link_options(core PUBLIC -fprofile-arcs -ftest-coverage)
  target_compile_options(protocol PUBLIC -O0 -g -fprofile-arcs -ftest-coverage)
  target_link_options(protocol PUBLIC -fprofile-arcs -ftest-coverage)
endif()
