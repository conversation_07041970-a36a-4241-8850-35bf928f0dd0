#include <doctest/doctest.h>
#include <zexuan/utils/invoke_id_utils.hpp>

#include <string>
#include <regex>

using namespace zexuan::utils::invoke_id;

TEST_CASE("invoke_id::Generate") {
    SUBCASE("Generate with custom sequence") {
        std::string result = Generate(123, "custom-seq");
        CHECK(result == "123#ZX#custom-seq");
    }
    
    SUBCASE("Generate with empty sequence (auto UUID)") {
        std::string result = Generate(456);
        
        // 检查格式是否正确 (数字#ZX#UUID)
        std::regex pattern(R"(\d+#ZX#[a-f0-9\-]+)");
        CHECK(std::regex_match(result, pattern));
        
        // 检查对象ID部分
        CHECK(result.substr(0, 3) == "456");
        CHECK(result.substr(3, 4) == "#ZX#");
    }
    
    SUBCASE("Generate with different object IDs") {
        std::string result1 = Generate(1, "test");
        std::string result2 = Generate(999, "test");
        
        CHECK(result1 == "1#ZX#test");
        CHECK(result2 == "999#ZX#test");
    }
}

TEST_CASE("invoke_id::ParseObjectId") {
    SUBCASE("Parse valid invoke_id") {
        CHECK(ParseObjectId("123#ZX#sequence") == 123);
        CHECK(ParseObjectId("456#ZX#another-seq") == 456);
        CHECK(ParseObjectId("1#ZX#test") == 1);
        CHECK(ParseObjectId("999#ZX#uuid-here") == 999);
    }
    
    SUBCASE("Parse invalid invoke_id") {
        CHECK(ParseObjectId("invalid-format") == -1);
        CHECK(ParseObjectId("123#WRONG#sequence") == -1);
        CHECK(ParseObjectId("abc#ZX#sequence") == -1);
        CHECK(ParseObjectId("") == -1);
        CHECK(ParseObjectId("123#ZX") == -1);  // 缺少序列号部分
    }
}

TEST_CASE("invoke_id::ParseSequence") {
    SUBCASE("Parse valid invoke_id") {
        CHECK(ParseSequence("123#ZX#my-sequence") == "my-sequence");
        CHECK(ParseSequence("456#ZX#uuid-1234-5678") == "uuid-1234-5678");
        CHECK(ParseSequence("1#ZX#test") == "test");
        CHECK(ParseSequence("999#ZX#") == "");  // 空序列号
    }
    
    SUBCASE("Parse invalid invoke_id") {
        CHECK(ParseSequence("invalid-format") == "");
        CHECK(ParseSequence("123#WRONG#sequence") == "");
        CHECK(ParseSequence("") == "");
        CHECK(ParseSequence("123#ZX") == "");  // 缺少序列号部分
    }
}

TEST_CASE("invoke_id::IsValid") {
    SUBCASE("Valid invoke_id formats") {
        CHECK(IsValid("123#ZX#sequence"));
        CHECK(IsValid("1#ZX#test"));
        CHECK(IsValid("999#ZX#uuid-1234-5678-9abc"));
        CHECK(IsValid("456#ZX#non-empty"));  // 序列号不能为空
        CHECK(IsValid("0#ZX#zero-id"));
    }
    
    SUBCASE("Invalid invoke_id formats") {
        CHECK_FALSE(IsValid(""));
        CHECK_FALSE(IsValid("invalid-format"));
        CHECK_FALSE(IsValid("123#WRONG#sequence"));
        CHECK_FALSE(IsValid("abc#ZX#sequence"));  // 非数字对象ID
        CHECK_FALSE(IsValid("123#ZX"));  // 缺少序列号部分
        CHECK_FALSE(IsValid("#ZX#sequence"));  // 缺少对象ID
        CHECK_FALSE(IsValid("123##sequence"));  // 错误的分隔符
        CHECK_FALSE(IsValid("123#ZX#sequence#extra"));  // 多余的部分
    }
}

TEST_CASE("invoke_id integration test") {
    SUBCASE("Generate and parse round trip") {
        int original_id = 12345;
        std::string original_seq = "test-sequence-123";
        
        // 生成 invoke_id
        std::string invoke_id = Generate(original_id, original_seq);
        
        // 验证格式
        CHECK(IsValid(invoke_id));
        
        // 解析并验证
        CHECK(ParseObjectId(invoke_id) == original_id);
        CHECK(ParseSequence(invoke_id) == original_seq);
    }
    
    SUBCASE("Generate with auto UUID and parse") {
        int original_id = 67890;
        
        // 生成带自动UUID的 invoke_id
        std::string invoke_id = Generate(original_id);
        
        // 验证格式
        CHECK(IsValid(invoke_id));
        
        // 解析对象ID
        CHECK(ParseObjectId(invoke_id) == original_id);
        
        // 序列号应该不为空（自动生成的UUID）
        std::string sequence = ParseSequence(invoke_id);
        CHECK_FALSE(sequence.empty());
    }
}
