# ZclMltSrvHbtMngr
CHeartbeatHandler 作为主控制器，管理多个广播器和监听器
CHeartbeat_Broadcaster 负责向指定地址发送心跳包
CHeartbeat_Listerner 负责监听心跳包，内部使用 CHeartbeat_Receiver 接收数据
CHeartbeat_Receiver 负责底层UDP数据接收

# ZclMltSrvOnlineMngr
CSrvOnlineManager 作为总控制器，协调各个子组件
CSrvSelfStatusMonitor 监控本地服务器状态并发送心跳
CSrvHeartbeatMonitor 监听其他服务器心跳并更新状态
CServerSwitchHandler 根据服务器状态执行切换逻辑

# ZcsServer

### common
通用定义和配置文件：CommuDef.h
线程同步工具：CsLocker.h
消息处理组件：SttpMsgProductor.h/cpp, ZxMsgCaster.h/cpp
观察者模式组件：ZxObserver.h/cpp, ZxPublisher.h/cpp
API接口定义：多个*API.h文件
服务管理组件：ZxSrvOnlineManagerWrapper.h/cpp
版本历史记录：ZxCommuServer_common_update_history.cpp