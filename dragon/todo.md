# ZclMltSrvHbtMngr
CHeartbeatHandler 作为主控制器，管理多个广播器和监听器
CHeartbeat_Broadcaster 负责向指定地址发送心跳包
CHeartbeat_Listerner 负责监听心跳包，内部使用 CHeartbeat_Receiver 接收数据
CHeartbeat_Receiver 负责底层UDP数据接收

# ZclMltSrvOnlineMngr
CSrvOnlineManager 作为总控制器，协调各个子组件
CSrvSelfStatusMonitor 监控本地服务器状态并发送心跳
CSrvHeartbeatMonitor 监听其他服务器心跳并更新状态
CServerSwitchHandler 根据服务器状态执行切换逻辑

# ZcsServer

### common
通用定义和配置文件：CommuDef.h
线程同步工具：CsLocker.h
消息处理组件：SttpMsgProductor.h/cpp, ZxMsgCaster.h/cpp
观察者模式组件：ZxObserver.h/cpp, ZxPublisher.h/cpp
API接口定义：多个*API.h文件
服务管理组件：ZxSrvOnlineManagerWrapper.h/cpp
版本历史记录：ZxCommuServer_common_update_history.cpp

### server

##### ZcsCliMngr

会话池管理：维护所有客户端会话的生命周期
协议库加载：动态加载不同的通信协议库
数据库交互：从数据库读取站点配置信息
命令处理：处理来自上层的控制命令
在线管理：配合服务器在线管理器实现主备切换

##### ZcsDataHdl

ZcsDataHdl（数据处理中心） 是 ZcsServer 通信服务器的核心数据处理组件，负责接收、分类、处理和存储来自各个子站的 STTP 协议消息。它是整个通信系统的数据中枢，承担着数据流转、消息分发和持久化存储的关键职责。